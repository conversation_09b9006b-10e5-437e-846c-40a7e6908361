import mongoose, { Schema, Document } from 'mongoose';

// 用户接口定义
export interface IUser extends Document {
  userId: string;           // 用户唯一标识
  fingerprint?: string;     // 浏览器指纹
  sessionId?: string;       // 会话ID
  
  // 用户偏好设置
  preferences: {
    language: 'zh' | 'en';
    theme: 'light' | 'dark';
    viewMode: 'grid' | 'list';
    itemsPerPage: number;
  };
  
  // 统计信息
  stats: {
    totalFavorites: number;
    lastActiveAt: Date;
    createdAt: Date;
  };
  
  // 元数据
  isActive: boolean;
  metadata?: {
    userAgent?: string;
    ipAddress?: string;
    source?: 'web' | 'mobile';
    [key: string]: any;
  };
}

// 用户Schema定义
const UserSchema = new Schema<IUser>({
  userId: {
    type: String,
    required: true,
    unique: true,
    index: true,
    trim: true
  },
  
  fingerprint: {
    type: String,
    index: true,
    trim: true
  },
  
  sessionId: {
    type: String,
    index: true,
    trim: true
  },
  
  // 用户偏好设置
  preferences: {
    language: {
      type: String,
      enum: ['zh', 'en'],
      default: 'zh'
    },
    theme: {
      type: String,
      enum: ['light', 'dark'],
      default: 'light'
    },
    viewMode: {
      type: String,
      enum: ['grid', 'list'],
      default: 'grid'
    },
    itemsPerPage: {
      type: Number,
      default: 20,
      min: 10,
      max: 100
    }
  },
  
  // 统计信息
  stats: {
    totalFavorites: {
      type: Number,
      default: 0,
      min: 0
    },
    lastActiveAt: {
      type: Date,
      default: Date.now,
      index: true
    },
    createdAt: {
      type: Date,
      default: Date.now
    }
  },
  
  // 元数据
  isActive: {
    type: Boolean,
    default: true,
    index: true
  },
  
  metadata: {
    userAgent: String,
    ipAddress: String,
    source: {
      type: String,
      enum: ['web', 'mobile'],
      default: 'web'
    }
  }
}, {
  timestamps: true,
  versionKey: false
});

// 索引优化
UserSchema.index({ fingerprint: 1, isActive: 1 });
UserSchema.index({ sessionId: 1, isActive: 1 });
UserSchema.index({ 'stats.lastActiveAt': -1 });

// 中间件：更新最后活跃时间
UserSchema.pre('save', function(next) {
  if (this.isModified() && !this.isModified('stats.lastActiveAt')) {
    this.stats.lastActiveAt = new Date();
  }
  next();
});

// 静态方法：根据指纹或会话ID查找或创建用户
UserSchema.statics.findOrCreateUser = async function(
  identifier: { fingerprint?: string; sessionId?: string; userAgent?: string; ipAddress?: string }
) {
  const { fingerprint, sessionId, userAgent, ipAddress } = identifier;

  // 优先使用指纹查找用户
  let user = null;
  if (fingerprint) {
    user = await this.findOne({ fingerprint, isActive: true });
  }

  // 如果没有找到，尝试使用会话ID查找
  if (!user && sessionId) {
    user = await this.findOne({ sessionId, isActive: true });
  }

  // 如果都没有找到，创建新用户
  if (!user) {
    const userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    user = new this({
      userId,
      fingerprint,
      sessionId,
      metadata: {
        userAgent,
        ipAddress,
        source: 'web'
      }
    });
    await user.save();
  } else {
    // 更新现有用户的会话信息
    if (sessionId && user.sessionId !== sessionId) {
      user.sessionId = sessionId;
    }
    if (userAgent && user.metadata?.userAgent !== userAgent) {
      user.metadata = { ...user.metadata, userAgent };
    }
    await user.save();
  }

  return user;
};

// 实例方法：更新收藏统计
UserSchema.methods.updateFavoriteStats = async function(delta: number = 0) {
  this.stats.totalFavorites = Math.max(0, this.stats.totalFavorites + delta);
  this.stats.lastActiveAt = new Date();
  return await this.save();
};

export const User = mongoose.model<IUser>('User', UserSchema);
